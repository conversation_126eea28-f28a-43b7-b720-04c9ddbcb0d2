# 微信小程序确认收货组件接入方案

## 1. 整体流程

### 1.1 流程图

```mermaid
graph TD
    A[用户下单支付] --> B[支付成功，资金冻结]
    B --> C[商家准备发货]
    C --> D[商家实际发货]
    D --> E[后端调用微信发货信息录入接口]
    E --> F[微信推送发货消息给用户]
    F --> G[商品到达用户]
    G --> H[用户点击确认收货按钮]
    H --> I[前端调用wx.openBusinessView]
    I --> J[打开微信确认收货组件]
    J --> K{用户操作}
    K -->|确认收货| L[微信处理确认收货]
    K -->|取消| M[返回小程序]
    L --> N[微信回调小程序App.onShow]
    N --> O[前端处理回调结果]
    O --> P[调用后端查询订单状态接口]
    P --> Q[确认收货成功，资金解冻结算]
    M --> R[用户可重新操作]

    subgraph "前端需要做的事"
        F1[1. 修改确认收货按钮逻辑]
        F2[2. 调用wx.openBusinessView打开组件]
        F3[3. 在App.vue添加onShow回调处理]
        F4[4. 处理组件返回结果]
        F5[5. 调用后端接口确认状态]
    end

    subgraph "后端需要做的事"
        B1[1. 发货时调用微信发货信息录入接口]
        B2[2. 提供查询订单发货状态接口]
        B3[3. 处理确认收货后的业务逻辑]
        B4[4. 存储微信支付的transaction_id]
        B5[5. 配置商户号等信息]
    end

    subgraph "关键时间节点"
        T1[支付成功 - 资金冻结]
        T2[实际发货 - 录入发货信息]
        T3[用户收货 - 确认收货]
        T4[确认收货 - 资金结算]
    end

    style A fill:#e1f5fe
    style B fill:#ffecb3
    style D fill:#ffecb3
    style L fill:#c8e6c9
    style Q fill:#c8e6c9
    style T1 fill:#ffcdd2
    style T2 fill:#ffcdd2
    style T3 fill:#ffcdd2
    style T4 fill:#ffcdd2
```

### 1.2 文字描述

```
用户下单支付 → 支付成功(资金冻结) → 商家实际发货 → 调用微信发货信息录入接口 →
微信推送发货消息 → 用户收到商品 → 点击确认收货 → 调用wx.openBusinessView →
打开微信确认收货组件 → 用户确认 → 微信回调App.onShow → 处理回调结果 →
调用后端查询状态 → 确认收货成功(资金结算)
```

## 2. 前端需要实现的功能

### 2.1 修改确认收货按钮逻辑
- 当前：调用自定义的 `confirmReceipt` API
- 修改为：调用 `wx.openBusinessView` 打开微信确认收货组件

### 2.2 调用微信确认收货组件
```javascript
wx.openBusinessView({
  businessType: 'weappOrderConfirm',
  extraData: {
    transaction_id: '微信支付单号',
    // 或者使用
    merchant_id: '商户号',
    merchant_trade_no: '商户订单号'
  },
  success() {
    // 成功打开组件
  },
  fail() {
    // 打开失败，降级处理
  }
});
```

### 2.3 在App.vue中添加onShow回调
```javascript
export default {
  onShow(options) {
    // 处理确认收货组件的回调
    if (options.referrerInfo && options.referrerInfo.appId === 'wx1183b055aeec94d1') {
      const { status, errormsg, req_extradata } = options.referrerInfo.extraData;
      
      if (status === 'success') {
        // 确认收货成功，调用后端接口确认状态
      } else if (status === 'fail') {
        // 确认收货失败
      } else if (status === 'cancel') {
        // 用户取消
      }
    }
  }
}
```

### 2.4 处理组件返回结果
- success: 用户确认收货成功
- fail: 用户确认收货失败  
- cancel: 用户取消操作

### 2.5 调用后端接口确认状态
确认收货成功后，需要调用后端的查询订单发货状态接口，再次确认是否完成确认收货。

## 3. 后端需要实现的功能

### 3.1 存储微信支付信息
订单表需要存储：
- `transaction_id`: 微信支付单号
- `merchant_id`: 商户号
- `merchant_trade_no`: 商户订单号

### 3.2 发货时调用微信发货信息录入接口
```
POST https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token=ACCESS_TOKEN
```

参数包括：
- order_key: 订单信息(transaction_id 或 merchant_id+merchant_trade_no)
- logistics_type: 物流模式(1-快递，2-同城，3-虚拟，4-自提)
- delivery_mode: 发货模式(1-统一发货，2-分拆发货)
- shipping_list: 物流信息列表
- upload_time: 上传时间
- payer: 支付者信息(openid)

### 3.3 提供查询订单发货状态接口
```
POST https://api.weixin.qq.com/wxa/sec/order/get_order?access_token=ACCESS_TOKEN
```

### 3.4 处理确认收货后的业务逻辑
- 更新订单状态
- 处理资金结算相关逻辑

## 4. 关键数据字段

### 4.1 必需的订单字段
- `transaction_id`: 微信支付单号（必需）
- `merchant_id`: 商户号（可选，与transaction_id二选一）
- `merchant_trade_no`: 商户订单号（配合merchant_id使用）
- `openid`: 用户openid（必需）

### 4.2 物流信息字段
- `tracking_no`: 物流单号
- `express_company`: 物流公司编码
- `item_desc`: 商品描述

## 5. 实现步骤

### 5.1 前端实现步骤
1. 修改 OrderItemCard.vue 中的确认收货逻辑
2. 在 App.vue 中添加 onShow 回调处理
3. 添加降级处理（兼容性检查）
4. 测试组件调用和回调处理

### 5.2 后端实现步骤
1. 确认订单表结构，添加必需字段
2. 实现微信发货信息录入接口调用
3. 实现查询订单发货状态接口
4. 配置微信小程序相关参数

## 6. 注意事项

### 6.1 兼容性
- 小程序版本库 >= 2.6.0
- 需要检查 wx.openBusinessView 是否存在

### 6.2 安全性
- 确认收货成功后必须调用后端接口再次确认
- 避免仅依赖前端回调结果

### 6.3 用户体验
- 提供降级方案（原有的确认收货方式）
- 处理各种异常情况（网络错误、组件打开失败等）

## 7. 当前缺失的信息

1. 订单数据中是否包含 `transaction_id`
2. 是否已配置微信商户号相关信息
3. 是否已开通微信小程序发货信息管理服务
