<template>
  <view class="agreement-container">
    <!-- 头部区域 - 简化版，使用uni-app原生导航栏 -->

    <!-- 主标题 -->
    <text class="main-title">{{ agreement.noticeTitle }}</text>

    <!-- 协议内容 -->
    <view class="content-section">
      <rich-text :nodes="agreement.noticeContent"></rich-text>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getAdoptionAgreement } from '@/api/user'

const agreement = ref({})

onLoad(async () => {
  const res = await getAdoptionAgreement()
  agreement.value = res.data
})

</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-primary: #0f0f0f;
$text-secondary: #333333;
$text-bold: #333333;
$background-color: #ffffff;

// 尺寸变量
$header-height: 160rpx;
$content-padding: 40rpx;
$text-line-height: 50rpx;


// 主容器
.agreement-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: $background-color;
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

// 头部区域 - 已简化，使用原生导航栏

// 主标题
.main-title {
  text-align: center;
  font-size: 38rpx;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  line-height: 45rpx;
  color: $primary-color;
  margin: 40rpx 0 0 0;
  padding: 0 $content-padding;
  display: block;
  width: 100%;
  box-sizing: border-box;
}

// 内容区域
.content-section {
  width: 100%;
  margin: 32rpx 0 0;
  padding: 0 $content-padding $content-padding $content-padding;
  flex: 1;
  box-sizing: border-box;
}

</style>
