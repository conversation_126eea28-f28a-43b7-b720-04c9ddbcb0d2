<template>
  <view class="tower-container">
    <!-- 背景图片 -->
    <image class="background-image" src="./home-bg.png" mode="widthFix" />

    <!-- 按钮区域 -->
    <view class="button-container">
      <view class="action-button" @click="handleButtonClick">
        <text class="button-text">认养一棵苹安树</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'

// 按钮点击处理
const handleButtonClick = () => {
  uni.switchTab({
    url: '/pages/tree/tree'
  })
}

// 分享给朋友
onShareAppMessage(() => {
  return {
    title: '我在延安有棵苹果树',
    path: '/pages/tower/tower',
  }
})

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: '我在延安有棵苹果树',
    query: '',
  }
})
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$button-width: 670rpx;
$button-height: 88rpx;
$button-radius: 326rpx;
$button-border: 2rpx solid rgba(221, 60, 41, 0.5);

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 页面容器
.tower-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// 背景图片
.background-image {
  width: 100%;
  min-height: 100vh;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
}

// 按钮容器
.button-container {
  position: fixed;
  bottom: 56rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  @include flex-center;

  .action-button {
    width: $button-width;
    height: $button-height;
    border-radius: $button-radius;
    border: $button-border;
    background-color: $white-color;
    @include flex-center;
    box-sizing: border-box;

    // 添加阴影增强视觉效果
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    .button-text {
      font-size: 32rpx;
      color: $primary-color;
      font-weight: 400;
    }
  }
}

// 响应式适配
@media screen and (max-width: 375px) {
  .action-button {
    width: calc(100vw - 80rpx);
    max-width: $button-width;
  }
}
</style>
