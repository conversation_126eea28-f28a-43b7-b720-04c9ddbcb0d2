<template>
  <view class="certificate-modal-overlay" @click.self="closeModal">
    <view class="modal-content">
      <view class="close-button-wrapper" @click="closeModal">
        <view class="close-button">
          <text class="close-button-text"> 返回 </text>
        </view>
      </view>

      <!-- 使用 Painter 作为唯一的证书展示和生成方式 -->
      <l-painter
        v-if="adoption"
        ref="painter"
        is-uni-modules
        css="width: 600rpx; height: 900rpx; position: relative;"
        @done="handlePainterDone"
      >
        <!-- 背景 -->
        <l-painter-image
          src="/static/certificate-bg.jpg"
          css="width: 600rpx; height: 900rpx; position: absolute; top: 0; left: 0;"
        ></l-painter-image>
        <!-- 内容容器 -->
        <l-painter-view css="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; padding: 40rpx; box-sizing: border-box;">
          <!-- 标题 -->
          <l-painter-text
            text="认养证书"
            css="margin-top: 64rpx; font-size: 64rpx; font-weight: 700; color: #dd3c29; line-height: 75rpx;"
          ></l-painter-text>
          <!-- 编号 -->
          <l-painter-text
            :text="`证书编号：${certificateId}`"
            css="margin-top: 16rpx; font-size: 22rpx; color: #dd3c29; line-height: 26rpx;"
          ></l-painter-text>
          <!-- 认养人姓名 -->
          <l-painter-view css="margin-top: 56rpx; display: flex; flex-direction: column; align-items: center;">
            <l-painter-text
              :text="userName"
              css="font-size: 36rpx; font-weight: 500; color: #1a1a1a; line-height: 42rpx;"
            ></l-painter-text>
            <l-painter-view css="width: 360rpx; height: 2rpx; background-color: rgba(221, 60, 41, 0.5); margin-top: 10rpx;"></l-painter-view>
          </l-painter-view>
          
          <!-- 证书正文 -->
          <l-painter-view css="margin-top: 20rpx; width: 436rpx; color: #3d3d3d; font-size: 22rpx;">
            <l-painter-text text="恭喜您，成功认养了位于" css="line-height: 40rpx;"></l-painter-text>
            <l-painter-text :text="location" css="font-weight: 700; line-height: 40rpx;"></l-painter-text>
            <l-painter-text text="的" css="line-height: 40rpx;"></l-painter-text>
            <l-painter-text :text="fruitTreeName" css="font-weight: 700; line-height: 40rpx;"></l-painter-text>
            <l-painter-text text="编号为" css=" line-height: 40rpx;"></l-painter-text>
            <l-painter-text :text="certificateId" css="color: #dd3c29; line-height: 40rpx;"></l-painter-text>
          </l-painter-view>

          <!-- 第二段 -->
          <l-painter-text
            :text="certContent"
            css="margin-top: 20rpx; width: 436rpx; text-align: left; line-height: 40rpx; color: #3d3d3d; font-size: 22rpx;"
          ></l-painter-text>

          <!-- 发证机构 -->
          <l-painter-view css="margin-top: 40rpx; width: 436rpx; display: flex; flex-direction: column; align-items: flex-start; gap: 10rpx; text-align: left; color: #3d3d3d; font-size: 22rpx;">
            <l-painter-text :text="issuerName"></l-painter-text>
            <l-painter-text :text="formattedDate"></l-painter-text>
          </l-painter-view>

        </l-painter-view>
      </l-painter>

      <view class="actions-wrapper">
        <button class="action-button save-button" @click="handleSave" :disabled="!isPainterDone">
          {{ isPainterDone ? '保存证书' : '渲染中...' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useUserStore } from '@/store/user'

const painter = ref(null)
const isPainterDone = ref(false)
const emit = defineEmits(['close'])

const props = defineProps({
  adoption: {
    type: Object,
    required: true
  }
})

const userStore = useUserStore()

const closeModal = () => {
  emit('close')
}

// 组件挂载时确保用户信息已加载
onMounted(async () => {
  // 如果用户信息不存在，尝试获取
  if (!userStore.userInfo) {
    try {
      await userStore.refreshUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
})

const handlePainterDone = () => {
  console.log('Painter is ready.')
  isPainterDone.value = true
}

// 计算属性：用户昵称
const userName = computed(() => {
  return props.adoption?.certUserName || userStore.userInfo?.nickName || '认养用户'
})

// 计算属性：证书编号
const certificateId = computed(() => {
  return props.adoption?.certCode || props.adoption?.orderSn || props.adoption?.id || 'CERT' + Date.now()
})

// 计算属性：果树名称
const fruitTreeName = computed(() => {
  return props.adoption?.certVariety || props.adoption?.fruitTreeName || '果树'
})

// 计算属性：地点
const location = computed(() => {
  return props.adoption?.certOrigin || '陕西延安'
})

// 计算属性：格式化的日期
const formattedDate = computed(() => {
  return props.adoption?.certIssueDate || (() => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    return `${year}年${month}月${day}日`
  })()
})

// 计算属性：发证机构
const issuerName = computed(() => {
  return props.adoption?.certIssuerName || '金壶口生态基金会'
})

// 计算属性：证书内容
const certContent = computed(() => {
  return props.adoption?.certContent || '绿荫泽被，生生不息。您的认养善举，不仅赋予了这棵树木特别的守护，更是为城市增添一抹生机，为大地播撒一份希望。感谢您对绿色家园的深情厚意，您的行动让我们的环境更加清新美好。'
})

const handleSave = () => {
  if (!painter.value || !isPainterDone.value) {
    uni.showToast({ title: '绘图组件尚未准备好，请稍候', icon: 'none' })
    return
  }
  
  uni.showLoading({ title: '证书生成中...' })

  painter.value.canvasToTempFilePath({
    fileType: 'jpg',
    quality: 1,
    pathType: 'url', // 确保返回的是临时文件路径，而不是base64
    success: (res) => {
      uni.saveImageToPhotosAlbum({
        filePath: res.tempFilePath,
        success: () => {
          uni.showToast({ title: '证书已保存', icon: 'success' })
        },
        fail: (err) => {
          if (err.errMsg?.includes('auth deny') || err.errMsg?.includes('auth denied')) {
            uni.showModal({
              title: '提示',
              content: '需要您授权保存相册',
              showCancel: false,
              success: () => uni.openSetting(),
            })
          } else {
            uni.showToast({ title: '保存失败，请稍后重试', icon: 'none' })
          }
        },
        complete: () => {
          uni.hideLoading()
        },
      })
    },
    fail: (err) => {
      uni.hideLoading()
      uni.showToast({ title: '生成失败', icon: 'none' })
      console.error('Failed to generate image:', err)
    },
  })
}
</script>

<style lang="scss" scoped>
.certificate-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.close-button-wrapper {
  margin-bottom: 32rpx;
  align-self: flex-start;
  margin-left: 75rpx;
}

.close-button {
  width: 166rpx;
  height: 64rpx;
  border-radius: 326rpx;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-button-text {
  font-size: 30rpx;
  color: #ffffff;
}

.actions-wrapper {
  margin-top: 32rpx;
}

.action-button {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  color: #ffffff;
  background-color: #dd3c29;
  border-radius: 40rpx;
  padding: 0 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;

  &[disabled] {
    background-color: #cccccc;
  }
}
</style>
