<template>
  <view class="fruit-detail-container" v-if="imageList.length > 0">
    <!-- 标题 -->
    <text class="detail-title">果树详情</text>

    <!-- 图片列表 -->
    <view class="image-list">
      <image
        v-for="(imageUrl, index) in imageList"
        :key="index"
        :src="imageUrl"
        class="detail-image"
        mode="widthFix"
      />
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  fruitDetail: {
    type: String,
    default: ''
  }
})

// 处理图片列表字符串
const imageList = computed(() => {
  if (!props.fruitDetail) return []

  // 将图片URL字符串按逗号分割成数组
  return props.fruitDetail.split(',').map(url => url.trim()).filter(url => url)
})
</script>

<style lang="scss" scoped>
// 设计变量
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-gray: #666666;
$border-color: #e5e5e5;

.fruit-detail-container {
  background-color: $white-color;
  margin: 20rpx 0;
  padding: 40rpx;
}

.detail-title {
  display: block;
  text-align: center;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 55rpx;
  color: $text-dark;
  margin-bottom: 30rpx;
}

.image-list {
  width: 100%;
}

.detail-image {
  width: 100%;
  margin-bottom: 20rpx;
  border-radius: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
