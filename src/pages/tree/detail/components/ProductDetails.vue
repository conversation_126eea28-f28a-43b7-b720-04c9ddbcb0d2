<template>
  <view class="details-container">
    <!-- 标题 -->
    <text class="details-title">商品详情</text>
    
    <!-- 详情表格 -->
    <view class="details-table">
      <view class="detail-row">
        <text class="detail-label">品牌</text>
        <text class="detail-value">{{ details.brand }}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">产品类型</text>
        <text class="detail-value">{{ details.productType }}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">单果直径</text>
        <text class="detail-value">{{ details.diameter }}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">保质期</text>
        <text class="detail-value">{{ details.shelfLife }}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">产地</text>
        <text class="detail-value">{{ details.origin }}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">规格包装</text>
        <text class="detail-value">{{ details.specPackage || '-' }}</text>
      </view>

      <view class="detail-row">
        <text class="detail-label">储存条件</text>
        <text class="detail-value">{{ details.storageCondition || '-' }}</text>
      </view>

      <view class="detail-row">
        <text class="detail-label">品种</text>
        <text class="detail-value">{{ details.variety }}</text>
      </view>

      <view class="detail-row">
        <text class="detail-label">发货时间</text>
        <text class="detail-value">{{ details.shippingTime || '-' }}</text>
      </view>

      <view class="detail-row">
        <text class="detail-label">认养周期</text>
        <text class="detail-value">{{ details.period }}个月</text>
      </view>

      <view class="detail-row">
        <text class="detail-label">日照时间</text>
        <text class="detail-value">{{ details.sunshineHours ? details.sunshineHours + '小时/天' : '-' }}</text>
      </view>

      <view class="detail-row">
        <text class="detail-label">发货时间</text>
        <text class="detail-value">{{ details.shippingTime || '-' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
// Props
const props = defineProps({
  details: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style lang="scss" scoped>
// 设计变量
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-gray: #767676;
$bg-light-gray: #f8f8f8;

.details-container {
  background-color: $white-color;
  padding: 24rpx 40rpx;
}

.details-title {
  display: block;
  text-align: center;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 55rpx;
  color: $text-dark;
  margin-bottom: 30rpx;
}

.details-table {
  background-color: $bg-light-gray;
  border-radius: 16rpx;
  padding: 30rpx 40rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 28rpx;
  line-height: 42rpx;
  color: $text-gray;
  flex-shrink: 0;
  margin-right: 40rpx;
}

.detail-value {
  font-size: 28rpx;
  line-height: 42rpx;
  color: $text-dark;
  text-align: right;
  flex: 1;
}
</style>
