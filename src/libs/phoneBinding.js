import { useUserStore } from '@/store/user'

/**
 * 检查用户是否已绑定手机号
 * @returns {Promise<boolean>} 是否已绑定手机号
 */
export async function isPhoneBound() {
  const userStore = useUserStore()

  // 如果用户信息不存在，先请求用户信息
  if (!userStore.userInfo) {
    await userStore.refreshUserInfo()
  }

  // 检查用户信息是否存在且手机号不为空
  return !!(userStore.userInfo && userStore.userInfo.phonenumber)
}

/**
 * 检查手机号绑定状态，如果未绑定则跳转到绑定页面
 * @returns {Promise<boolean>} 是否已绑定（true表示已绑定，false表示未绑定并已跳转）
 */
export async function checkPhoneBindingAndRedirect() {
  if (await isPhoneBound()) {
    return true
  }

  // 未绑定手机号，跳转到绑定页面（保留页面栈，绑定成功后可以返回）
  uni.navigateTo({
    url: '/pages/auth/bind-phone/bind-phone'
  })

  return false
}
